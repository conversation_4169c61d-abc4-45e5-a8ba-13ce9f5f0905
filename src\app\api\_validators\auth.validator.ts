import { NextRequest, NextResponse } from 'next/server';
import { z } from 'zod';
import { errorResponse } from '@/utils/response';
import { ErrorCode } from '@/constants/error-codes';

// Key authentication request schema (supports both new and legacy formats)
export const keyAuthSchema = z.object({
  accessKey: z.string()
    .min(1, 'Access key is required')
    .refine((key) => {
      // New format: 8 words + 10 digits
      const newFormat = /^[a-z]+-[a-z]+-[a-z]+-[a-z]+-[a-z]+-[a-z]+-[a-z]+-[a-z]+-\d{10}$/.test(key);
      // Legacy format: 2 words + 2 digits
      const legacyFormat = /^[a-z]+-[a-z]+-\d{2}$/.test(key);
      return newFormat || legacyFormat;
    }, 'Invalid access key format. Expected format: 8-words-10digits or legacy 2-words-2digits'),
});

// Recovery code authentication request schema
export const recoveryCodeAuthSchema = z.object({
  accessKey: z.string()
    .min(1, 'Access key is required')
    .refine((key) => {
      const newFormat = /^[a-z]+-[a-z]+-[a-z]+-[a-z]+-[a-z]+-[a-z]+-[a-z]+-[a-z]+-\d{10}$/.test(key);
      const legacyFormat = /^[a-z]+-[a-z]+-\d{2}$/.test(key);
      return newFormat || legacyFormat;
    }, 'Invalid access key format'),
  recoveryCode: z.string()
    .min(1, 'Recovery code is required')
    .refine((code) => {
      // Support both old 8-character format and new 20-character format
      const oldFormat = /^[A-F0-9]{8}$/.test(code.toUpperCase());
      const newFormat = /^[A-Z0-9]{20}$/.test(code.toUpperCase());
      return oldFormat || newFormat;
    }, 'Invalid recovery code format. Expected format: 8 or 20 uppercase alphanumeric characters'),
});

// Recovery-only authentication request schema (for creating new access key with recovery code)
export const recoveryOnlyAuthSchema = z.object({
  recoveryCode: z.string()
    .min(1, 'Recovery code is required')
    .refine((code) => {
      // Support both old 8-character format and new 20-character format
      const oldFormat = /^[A-F0-9]{8}$/.test(code.toUpperCase());
      const newFormat = /^[A-Z0-9]{20}$/.test(code.toUpperCase());
      return oldFormat || newFormat;
    }, 'Invalid recovery code format. Expected format: 8 or 20 uppercase alphanumeric characters'),
  name: z.string()
    .trim()
    .max(50, 'Name cannot be longer than 50 characters')
    .optional(),
});

// Key regeneration request schema
export const keyRegenerationSchema = z.object({
  currentAccessKey: z.string()
    .min(1, 'Current access key is required')
    .refine((key) => {
      const newFormat = /^[a-z]+-[a-z]+-[a-z]+-[a-z]+-[a-z]+-[a-z]+-[a-z]+-[a-z]+-\d{10}$/.test(key);
      const legacyFormat = /^[a-z]+-[a-z]+-\d{2}$/.test(key);
      return newFormat || legacyFormat;
    }, 'Invalid current access key format'),
  newAccessKey: z.string()
    .refine((key) => {
      const newFormat = /^[a-z]+-[a-z]+-[a-z]+-[a-z]+-[a-z]+-[a-z]+-[a-z]+-[a-z]+-\d{10}$/.test(key);
      const legacyFormat = /^[a-z]+-[a-z]+-\d{2}$/.test(key);
      return newFormat || legacyFormat;
    }, 'Invalid new access key format')
    .optional(),
});

/**
 * Validate key authentication request
 */
export async function validateKeyAuth(req: NextRequest): Promise<{ data: z.infer<typeof keyAuthSchema> } | NextResponse> {
  try {
    const body = await req.json();
    const result = keyAuthSchema.safeParse(body);

    if (!result.success) {
      const errors = result.error.errors.map(err => `${err.path.join('.')}: ${err.message}`).join(', ');
      return errorResponse(errors, ErrorCode.VALIDATION_ERROR, 400);
    }

    return { data: result.data };
  } catch (error) {
    return errorResponse('Invalid request body', ErrorCode.BAD_REQUEST, 400);
  }
}

/**
 * Validate recovery code authentication request
 */
export async function validateRecoveryCodeAuth(req: NextRequest): Promise<{ data: z.infer<typeof recoveryCodeAuthSchema> } | NextResponse> {
  try {
    const body = await req.json();
    const result = recoveryCodeAuthSchema.safeParse(body);

    if (!result.success) {
      const errors = result.error.errors.map(err => `${err.path.join('.')}: ${err.message}`).join(', ');
      return errorResponse(errors, ErrorCode.VALIDATION_ERROR, 400);
    }

    return { data: result.data };
  } catch (error) {
    return errorResponse('Invalid request body', ErrorCode.BAD_REQUEST, 400);
  }
}

/**
 * Validate recovery-only authentication request
 */
export async function validateRecoveryOnlyAuth(req: NextRequest): Promise<{ data: z.infer<typeof recoveryOnlyAuthSchema> } | NextResponse> {
  try {
    const body = await req.json();
    const result = recoveryOnlyAuthSchema.safeParse(body);

    if (!result.success) {
      const errors = result.error.errors.map(err => `${err.path.join('.')}: ${err.message}`).join(', ');
      return errorResponse(errors, ErrorCode.VALIDATION_ERROR, 400);
    }

    return { data: result.data };
  } catch (error) {
    return errorResponse('Invalid request body', ErrorCode.BAD_REQUEST, 400);
  }
}

/**
 * Validate key regeneration request
 */
export async function validateKeyRegeneration(req: NextRequest): Promise<{ data: z.infer<typeof keyRegenerationSchema> } | NextResponse> {
  try {
    const body = await req.json();
    const result = keyRegenerationSchema.safeParse(body);

    if (!result.success) {
      const errors = result.error.errors.map(err => `${err.path.join('.')}: ${err.message}`).join(', ');
      return errorResponse(errors, ErrorCode.VALIDATION_ERROR, 400);
    }

    return { data: result.data };
  } catch (error) {
    return errorResponse('Invalid request body', ErrorCode.BAD_REQUEST, 400);
  }
}

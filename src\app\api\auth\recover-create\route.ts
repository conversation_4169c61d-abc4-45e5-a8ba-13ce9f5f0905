import { NextRequest } from 'next/server';
import { recoveryOnlyAuthSchema } from '@/app/api/_validators/auth.validator';
import { authController } from '@/core/controllers';
import logger from '@/utils/logger';
import { errorResponse } from '@/utils/response';
import { ErrorCode } from '@/constants/error-codes';

// Tell Next.js this route should always be dynamically rendered
export const dynamic = 'force-dynamic';

/**
 * POST /api/auth/recover-create - Create new access key using only recovery code
 */
export async function POST(req: NextRequest) {
  try {
    // Parse request body once
    const body = await req.json().catch(() => ({}));

    // Validate recovery-only request
    const result = recoveryOnlyAuthSchema.safeParse(body);
    if (!result.success) {
      const errors = result.error.errors.map(err => `${err.path.join('.')}: ${err.message}`).join(', ');
      return errorResponse(errors, ErrorCode.VALIDATION_ERROR, 400);
    }

    // Process recovery-only authentication
    return authController.createAccessKeyWithRecovery(result.data, req);
  } catch (error) {
    logger.error('Recovery-create error:', error);
    return errorResponse(
      'An unexpected error occurred',
      ErrorCode.INTERNAL_SERVER_ERROR,
      500
    );
  }
}

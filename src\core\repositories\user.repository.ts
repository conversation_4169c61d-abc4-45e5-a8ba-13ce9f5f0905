import User, { IUser } from '@/db/models/User';
import connectDB from '@/config/database';
import { UserProfile } from '@/types/auth';
import mongoose from 'mongoose';
import { ApiError } from '@/core/errors/api-error';
import { ErrorCode } from '@/constants/error-codes';
import logger from '@/utils/logger';

/**
 * User repository for database operations
 */
export class UserRepository {
  /**
   * Find user by ID
   * @param id User ID
   * @returns User or null
   */
  async findById(id: string): Promise<IUser | null> {
    try {
      await connectDB();
      return User.findById(id);
    } catch (error) {
      logger.error('Find user by ID error:', error);
      throw new ApiError(ErrorCode.DATABASE_ERROR, 'Database error', 500);
    }
  }

  /**
   * Find user by access key (secure method using hash comparison)
   * @param accessKey User access key
   * @returns User or null
   */
  async findByAccessKey(accessKey: string): Promise<IUser | null> {
    try {
      logger.info(`Finding user by access key hash comparison`);
      await connectDB();

      // First try to find by plain access key for backward compatibility
      let user = await User.findOne({ accessKey: accessKey.toLowerCase() });
      if (user) {
        logger.info(`User found with legacy plain access key`);
        return user;
      }

      // If not found by plain key, search by hash comparison (more secure)
      logger.info('Searching users by hash comparison');
      const users = await User.find({}).select('accessKeyHash');

      for (const userDoc of users) {
        const { compareAccessKey } = await import('@/utils/auth');
        const isMatch = await compareAccessKey(accessKey, userDoc.accessKeyHash);
        if (isMatch) {
          logger.info(`User found with hash comparison`);
          return await User.findById(userDoc._id);
        }
      }

      logger.info(`No user found with provided access key`);
      return null;
    } catch (error) {
      logger.error('Find user by access key error:', error);
      throw new ApiError(ErrorCode.DATABASE_ERROR, 'Database error', 500);
    }
  }

  /**
   * Create new user
   * @param userData User data
   * @returns Created user
   */
  async create(userData: {
    accessKey: string;
    accessKeyHash?: string;
    name?: string;
    recoveryCodes?: string[];
    usedRecoveryCodes?: number[];
    authAttempts?: any;
  }): Promise<IUser> {
    try {
      logger.info(`Creating new user with access key: ${userData.accessKey}`);
      await connectDB();
      logger.info('Database connected, creating new user instance');
      const user = new User(userData);
      logger.info('Saving user to database');
      const savedUser = await user.save();
      logger.info(`User created successfully with ID: ${savedUser._id}`);
      return savedUser;
    } catch (error) {
      logger.error('Create user error:', error);

      // Check for duplicate key error (MongoDB error code 11000)
      if (error instanceof mongoose.Error.ValidationError) {
        logger.error('Validation error:', error.message);
        throw new ApiError(ErrorCode.VALIDATION_ERROR, error.message, 400);
      } else if (error instanceof Error && 'code' in error && (error as any).code === 11000) {
        logger.error('Duplicate key error (user already exists)');
        throw new ApiError(ErrorCode.USER_ALREADY_EXISTS, 'User already exists', 409);
      }

      throw new ApiError(ErrorCode.DATABASE_ERROR, 'Database error', 500);
    }
  }

  /**
   * Update user password
   * @param id User ID
   * @param password New password
   * @returns Updated user
   */
  async updatePassword(id: string, password: string): Promise<IUser | null> {
    try {
      await connectDB();
      const user = await User.findById(id);
      if (!user) return null;

      user.password = password;
      return user.save();
    } catch (error) {
      logger.error('Update password error:', error);
      throw new ApiError(ErrorCode.DATABASE_ERROR, 'Database error', 500);
    }
  }

  /**
   * Get user profile
   * @param id User ID
   * @returns User profile or null
   */
  async getProfile(id: string): Promise<UserProfile | null> {
    try {
      await connectDB();
      const user = await User.findById(id);
      if (!user) return null;

      console.log('UserRepository.getProfile - Raw user data:', JSON.stringify(user.toObject(), null, 2));

      const userId = user._id ? user._id.toString() : '';
      const profile = {
        id: userId,
        accessKey: `user_${userId}`, // Use user ID instead of plain access key
        name: user.name || '',
        preferences: user.preferences
      };

      console.log('UserRepository.getProfile - Formatted profile:', JSON.stringify(profile, null, 2));

      return profile;
    } catch (error) {
      logger.error('Get profile error:', error);
      throw new ApiError(ErrorCode.DATABASE_ERROR, 'Database error', 500);
    }
  }

  /**
   * Find users with IP attempts for rate limiting
   * @param ip IP address to search for
   * @returns Array of users with IP attempts
   */
  async findUsersWithIPAttempts(ip: string): Promise<IUser[]> {
    try {
      await connectDB();
      return User.find({
        [`authAttempts.ipAttempts.${ip}`]: { $exists: true }
      });
    } catch (error) {
      logger.error('Find users with IP attempts error:', error);
      throw new ApiError(ErrorCode.DATABASE_ERROR, 'Database error', 500);
    }
  }

  /**
   * Check if access key exists (secure method using hash comparison)
   * @param accessKey Access key to check
   * @returns True if exists, false otherwise
   */
  async accessKeyExists(accessKey: string): Promise<boolean> {
    try {
      await connectDB();

      // First try to find by plain access key for backward compatibility
      let user = await User.findOne({ accessKey: accessKey.toLowerCase() });
      if (user) {
        return true;
      }

      // If not found by plain key, search by hash comparison (more secure)
      const users = await User.find({}).select('accessKeyHash');

      for (const userDoc of users) {
        const { compareAccessKey } = await import('@/utils/auth');
        const isMatch = await compareAccessKey(accessKey, userDoc.accessKeyHash);
        if (isMatch) {
          return true;
        }
      }

      return false;
    } catch (error) {
      logger.error('Check access key exists error:', error);
      throw new ApiError(ErrorCode.DATABASE_ERROR, 'Database error', 500);
    }
  }

  /**
   * Update user access key hash (secure method)
   * @param id User ID
   * @param newAccessKey New access key (not stored in plain text)
   * @param newAccessKeyHash New access key hash
   * @returns Updated user or null
   */
  async updateAccessKey(id: string, newAccessKey: string, newAccessKeyHash: string): Promise<IUser | null> {
    try {
      await connectDB();
      const user = await User.findByIdAndUpdate(
        id,
        {
          accessKey: `user_${id}_${Date.now()}`, // Non-sensitive identifier
          accessKeyHash: newAccessKeyHash
        },
        { new: true }
      );
      return user;
    } catch (error) {
      logger.error('Update access key error:', error);
      throw new ApiError(ErrorCode.DATABASE_ERROR, 'Database error', 500);
    }
  }

  /**
   * Find users with IP attempts for rate limiting
   * @param ip IP address to search for
   * @returns Users with IP attempts
   */
  async findUsersWithIPAttempts(ip: string): Promise<IUser[]> {
    try {
      await connectDB();
      const users = await User.find({
        [`authAttempts.ipAttempts.${ip}`]: { $exists: true }
      });
      return users;
    } catch (error) {
      logger.error('Find users with IP attempts error:', error);
      throw new ApiError(ErrorCode.DATABASE_ERROR, 'Database error', 500);
    }
  }

  /**
   * Find all users (used for recovery code search)
   * @returns All users
   */
  async findAllUsers(): Promise<IUser[]> {
    try {
      await connectDB();
      const users = await User.find({});
      return users;
    } catch (error) {
      logger.error('Find all users error:', error);
      throw new ApiError(ErrorCode.DATABASE_ERROR, 'Database error', 500);
    }
  }

  /**
   * Update user recovery codes
   * @param userId User ID
   * @param hashedRecoveryCodes New hashed recovery codes
   * @returns Updated user or null
   */
  async updateRecoveryCodes(userId: string, hashedRecoveryCodes: string[]): Promise<IUser | null> {
    try {
      await connectDB();
      return User.findByIdAndUpdate(
        userId,
        {
          recoveryCodes: hashedRecoveryCodes,
          usedRecoveryCodes: [] // Reset used recovery codes
        },
        { new: true }
      );
    } catch (error) {
      logger.error('Update recovery codes error:', error);
      throw new ApiError(ErrorCode.DATABASE_ERROR, 'Database error', 500);
    }
  }

  /**
   * Find all users (for recovery code matching)
   * @returns Array of all users
   */
  async findAllUsers(): Promise<IUser[]> {
    try {
      await connectDB();
      return User.find({});
    } catch (error) {
      logger.error('Find all users error:', error);
      throw new ApiError(ErrorCode.DATABASE_ERROR, 'Database error', 500);
    }
  }
}

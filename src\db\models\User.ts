import mongoose from 'mongoose';
import { userSchema } from '@/db/schemas/user.schema';

/**
 * Custom time block interface
 */
export interface ICustomTimeBlock {
  startTime: string;
  endTime: string;
}

/**
 * User preferences interface
 */
export interface IUserPreferences {
  timeInterval: string;
  startHour: string;
  endHour: string;
  timeFormat: string;
  darkMode: boolean;
  syncEnabled: boolean;
  emailNotifications: boolean;
  customTimeBlocks?: ICustomTimeBlock[];
  useCustomTimeBlocks?: boolean;
}

/**
 * Authentication attempts tracking interface
 */
export interface IAuthAttempts {
  failedAttempts: number;
  lastFailedAttempt?: Date;
  lockUntil?: Date;
  ipAttempts: Map<string, {
    count: number;
    lastAttempt: Date;
    lockUntil?: Date;
    recoveryAttempts?: number;
    recoveryLockUntil?: Date;
  }>;
}

/**
 * User document interface
 */
export interface IUser extends mongoose.Document {
  accessKey: string;
  accessKeyHash: string;
  name?: string;
  recoveryCodes: string[];
  usedRecoveryCodes: number[];
  authAttempts: IAuthAttempts;
  preferences: IUserPreferences;
}

/**
 * User model
 */
const User = (mongoose.models.User || mongoose.model<IUser>('User', userSchema));

export default User;

import { NextRequest } from 'next/server';
import { userRepository } from '@/core/repositories';
import { ApiError } from '@/core/errors/api-error';
import { ErrorCode } from '@/constants/error-codes';
import logger from '@/utils/logger';

/**
 * Rate limiting configuration
 */
export const RATE_LIMIT_CONFIG = {
  // Maximum failed attempts before account lockout
  MAX_FAILED_ATTEMPTS: 3, // Reduced from 5 for stronger security

  // Maximum failed attempts per IP before IP lockout
  MAX_IP_ATTEMPTS: 5, // Reduced from 10 for stronger security

  // Maximum recovery code attempts before permanent lockout
  MAX_RECOVERY_ATTEMPTS: 3, // Very strict for recovery codes

  // Maximum recovery code attempts per IP
  MAX_RECOVERY_IP_ATTEMPTS: 5,

  // Base lockout duration in minutes
  BASE_LOCKOUT_DURATION: 30, // Increased from 15 minutes

  // Recovery code lockout duration in minutes (longer)
  RECOVERY_LOCKOUT_DURATION: 60, // 1 hour minimum for recovery attempts

  // Maximum lockout duration in minutes
  MAX_LOCKOUT_DURATION: 24 * 60, // 24 hours

  // Exponential backoff multiplier
  BACKOFF_MULTIPLIER: 3, // Increased from 2 for faster escalation

  // Time window for IP attempts in minutes
  IP_ATTEMPT_WINDOW: 60,

  // Time window for recovery attempts in minutes (longer)
  RECOVERY_ATTEMPT_WINDOW: 24 * 60, // 24 hours for recovery attempts
};

/**
 * Get client IP address from request
 * @param req Next.js request
 * @returns Client IP address
 */
export function getClientIP(req: NextRequest): string {
  // Check various headers for the real IP
  const forwarded = req.headers.get('x-forwarded-for');
  const realIP = req.headers.get('x-real-ip');
  const cfConnectingIP = req.headers.get('cf-connecting-ip');
  
  if (forwarded) {
    // x-forwarded-for can contain multiple IPs, take the first one
    return forwarded.split(',')[0].trim();
  }
  
  if (realIP) {
    return realIP;
  }
  
  if (cfConnectingIP) {
    return cfConnectingIP;
  }
  
  // Fallback to connection remote address
  // NextRequest doesn't have ip property, so we'll use 'unknown' as fallback
  return 'unknown';
}

/**
 * Calculate lockout duration based on failed attempts
 * @param failedAttempts Number of failed attempts
 * @param isRecoveryAttempt Whether this is a recovery code attempt
 * @returns Lockout duration in milliseconds
 */
export function calculateLockoutDuration(failedAttempts: number, isRecoveryAttempt: boolean = false): number {
  const baseMinutes = isRecoveryAttempt
    ? RATE_LIMIT_CONFIG.RECOVERY_LOCKOUT_DURATION
    : RATE_LIMIT_CONFIG.BASE_LOCKOUT_DURATION;
  const maxMinutes = RATE_LIMIT_CONFIG.MAX_LOCKOUT_DURATION;

  // Exponential backoff: for recovery attempts 1hr, 3hr, 9hr, 24hr (max)
  // For regular attempts: 30min, 1.5hr, 4.5hr, 13.5hr, 24hr (max)
  const minutes = Math.min(
    baseMinutes * Math.pow(RATE_LIMIT_CONFIG.BACKOFF_MULTIPLIER, failedAttempts - 1),
    maxMinutes
  );

  return minutes * 60 * 1000; // Convert to milliseconds
}

/**
 * Check if an account is currently locked
 * @param accessKey Access key to check
 * @returns Lock status and remaining time
 */
export async function checkAccountLock(accessKey: string): Promise<{
  isLocked: boolean;
  remainingTime?: number;
  reason?: string;
}> {
  try {
    const user = await userRepository.findByAccessKey(accessKey);
    
    if (!user || !user.authAttempts) {
      return { isLocked: false };
    }
    
    const { lockUntil, failedAttempts } = user.authAttempts;
    
    if (lockUntil && new Date() < lockUntil) {
      const remainingTime = lockUntil.getTime() - Date.now();
      return {
        isLocked: true,
        remainingTime,
        reason: `Account locked due to ${failedAttempts} failed attempts`
      };
    }
    
    return { isLocked: false };
  } catch (error) {
    logger.error('Error checking account lock:', error);
    return { isLocked: false };
  }
}

/**
 * Check if an IP is currently rate limited
 * @param req Next.js request
 * @returns Rate limit status
 */
export async function checkIPRateLimit(req: NextRequest): Promise<{
  isLimited: boolean;
  remainingTime?: number;
  reason?: string;
}> {
  const clientIP = getClientIP(req);
  
  try {
    // For IP rate limiting, we'll use a simple in-memory store for now
    // In production, you might want to use Redis or a database
    const users = await userRepository.findUsersWithIPAttempts(clientIP);
    
    let totalAttempts = 0;
    let latestLockUntil: Date | null = null;
    
    for (const user of users) {
      const ipAttempt = user.authAttempts.ipAttempts.get(clientIP);
      if (ipAttempt) {
        // Check if attempt is within the time window
        const timeSinceLastAttempt = Date.now() - ipAttempt.lastAttempt.getTime();
        const windowMs = RATE_LIMIT_CONFIG.IP_ATTEMPT_WINDOW * 60 * 1000;
        
        if (timeSinceLastAttempt <= windowMs) {
          totalAttempts += ipAttempt.count;
        }
        
        if (ipAttempt.lockUntil && (!latestLockUntil || ipAttempt.lockUntil > latestLockUntil)) {
          latestLockUntil = ipAttempt.lockUntil;
        }
      }
    }
    
    // Check if IP is currently locked
    if (latestLockUntil && new Date() < latestLockUntil) {
      const remainingTime = latestLockUntil.getTime() - Date.now();
      return {
        isLimited: true,
        remainingTime,
        reason: `IP rate limited due to ${totalAttempts} failed attempts`
      };
    }
    
    // Check if IP has exceeded attempt limit
    if (totalAttempts >= RATE_LIMIT_CONFIG.MAX_IP_ATTEMPTS) {
      return {
        isLimited: true,
        reason: `IP rate limited: ${totalAttempts} attempts in ${RATE_LIMIT_CONFIG.IP_ATTEMPT_WINDOW} minutes`
      };
    }
    
    return { isLimited: false };
  } catch (error) {
    logger.error('Error checking IP rate limit:', error);
    return { isLimited: false };
  }
}

/**
 * Record a failed recovery code attempt
 * @param req Next.js request for IP tracking
 */
export async function recordFailedRecoveryAttempt(req: NextRequest): Promise<void> {
  const clientIP = getClientIP(req);

  try {
    // For recovery attempts, we track by IP across all users since we don't know which user yet
    const users = await userRepository.findUsersWithIPAttempts(clientIP);

    // Find or create a user record to track this IP's recovery attempts
    let targetUser = users.length > 0 ? users[0] : null;

    if (!targetUser) {
      // If no users found with this IP, we'll need to create a temporary tracking record
      // For now, we'll just log and return since we can't create a user without an access key
      logger.warn(`Recovery attempt from unknown IP ${clientIP} - no user record to update`);
      return;
    }

    // Initialize authAttempts if not present
    if (!targetUser.authAttempts) {
      targetUser.authAttempts = {
        failedAttempts: 0,
        ipAttempts: new Map(),
      };
    }

    // Update IP-level recovery attempts
    const currentIPAttempt = targetUser.authAttempts.ipAttempts.get(clientIP) || {
      count: 0,
      lastAttempt: new Date(),
      recoveryAttempts: 0,
    };

    currentIPAttempt.recoveryAttempts = (currentIPAttempt.recoveryAttempts || 0) + 1;
    currentIPAttempt.lastAttempt = new Date();

    // Set IP recovery lockout if threshold exceeded
    if (currentIPAttempt.recoveryAttempts >= RATE_LIMIT_CONFIG.MAX_RECOVERY_IP_ATTEMPTS) {
      const lockoutDuration = calculateLockoutDuration(currentIPAttempt.recoveryAttempts, true);
      currentIPAttempt.recoveryLockUntil = new Date(Date.now() + lockoutDuration);

      logger.warn(`IP recovery locked for ${clientIP}: ${currentIPAttempt.recoveryAttempts} failed recovery attempts`);
    }

    targetUser.authAttempts.ipAttempts.set(clientIP, currentIPAttempt);

    // Save the updated user
    await targetUser.save();

    logger.info(`Recorded failed recovery attempt from IP ${clientIP}`);
  } catch (error) {
    logger.error('Error recording failed recovery attempt:', error);
  }
}

/**
 * Record a failed authentication attempt
 * @param accessKey Access key that failed
 * @param req Next.js request for IP tracking
 */
export async function recordFailedAttempt(accessKey: string, req: NextRequest): Promise<void> {
  const clientIP = getClientIP(req);

  try {
    const user = await userRepository.findByAccessKey(accessKey);

    if (!user) {
      logger.warn(`Attempted to record failed attempt for non-existent access key`);
      return;
    }

    // Initialize authAttempts if not present
    if (!user.authAttempts) {
      user.authAttempts = {
        failedAttempts: 0,
        ipAttempts: new Map(),
      };
    }

    // Update account-level failed attempts
    user.authAttempts.failedAttempts += 1;
    user.authAttempts.lastFailedAttempt = new Date();

    // Set account lockout if threshold exceeded
    if (user.authAttempts.failedAttempts >= RATE_LIMIT_CONFIG.MAX_FAILED_ATTEMPTS) {
      const lockoutDuration = calculateLockoutDuration(user.authAttempts.failedAttempts);
      user.authAttempts.lockUntil = new Date(Date.now() + lockoutDuration);

      logger.warn(`Account locked: ${user.authAttempts.failedAttempts} failed attempts`);
    }

    // Update IP-level attempts
    const currentIPAttempt = user.authAttempts.ipAttempts.get(clientIP) || {
      count: 0,
      lastAttempt: new Date(),
    };

    currentIPAttempt.count += 1;
    currentIPAttempt.lastAttempt = new Date();

    // Set IP lockout if threshold exceeded
    if (currentIPAttempt.count >= RATE_LIMIT_CONFIG.MAX_IP_ATTEMPTS) {
      const lockoutDuration = calculateLockoutDuration(currentIPAttempt.count);
      currentIPAttempt.lockUntil = new Date(Date.now() + lockoutDuration);

      logger.warn(`IP locked for ${clientIP}: ${currentIPAttempt.count} failed attempts`);
    }

    user.authAttempts.ipAttempts.set(clientIP, currentIPAttempt);

    // Save the updated user
    await user.save();

    logger.info(`Recorded failed attempt from IP ${clientIP}`);
  } catch (error) {
    logger.error('Error recording failed attempt:', error);
  }
}

/**
 * Reset failed attempts for successful authentication
 * @param accessKey Access key that successfully authenticated
 */
export async function resetFailedAttempts(accessKey: string): Promise<void> {
  try {
    const user = await userRepository.findByAccessKey(accessKey);
    
    if (!user) {
      return;
    }
    
    // Reset account-level attempts
    if (user.authAttempts) {
      user.authAttempts.failedAttempts = 0;
      user.authAttempts.lastFailedAttempt = undefined;
      user.authAttempts.lockUntil = undefined;
      
      await user.save();
      
      logger.info(`Reset failed attempts for access key ${accessKey}`);
    }
  } catch (error) {
    logger.error('Error resetting failed attempts:', error);
  }
}

/**
 * Validate recovery code attempt against stricter rate limits
 * @param req Next.js request
 * @throws ApiError if rate limited
 */
export async function validateRecoveryAttempt(req: NextRequest): Promise<void> {
  const clientIP = getClientIP(req);

  try {
    // Check global recovery attempt rate limiting by IP
    const users = await userRepository.findUsersWithIPAttempts(clientIP);

    let totalRecoveryAttempts = 0;
    let latestRecoveryLockUntil: Date | null = null;

    for (const user of users) {
      const ipAttempt = user.authAttempts.ipAttempts.get(clientIP);
      if (ipAttempt && ipAttempt.recoveryAttempts) {
        // Check if attempt is within the recovery time window (24 hours)
        const timeSinceLastAttempt = Date.now() - ipAttempt.lastAttempt.getTime();
        const windowMs = RATE_LIMIT_CONFIG.RECOVERY_ATTEMPT_WINDOW * 60 * 1000;

        if (timeSinceLastAttempt <= windowMs) {
          totalRecoveryAttempts += ipAttempt.recoveryAttempts;
        }

        if (ipAttempt.recoveryLockUntil && (!latestRecoveryLockUntil || ipAttempt.recoveryLockUntil > latestRecoveryLockUntil)) {
          latestRecoveryLockUntil = ipAttempt.recoveryLockUntil;
        }
      }
    }

    // Check if IP is currently locked for recovery attempts
    if (latestRecoveryLockUntil && new Date() < latestRecoveryLockUntil) {
      const remainingTime = latestRecoveryLockUntil.getTime() - Date.now();
      const remainingHours = Math.ceil(remainingTime / (60 * 60 * 1000));
      throw ApiError.tooManyRequests(
        `Recovery attempts blocked from this IP. Try again in ${remainingHours} hours.`,
        ErrorCode.IP_RATE_LIMITED
      );
    }

    // Check if IP has exceeded recovery attempt limit
    if (totalRecoveryAttempts >= RATE_LIMIT_CONFIG.MAX_RECOVERY_IP_ATTEMPTS) {
      throw ApiError.tooManyRequests(
        `Too many recovery attempts from this IP. Try again in 24 hours.`,
        ErrorCode.IP_RATE_LIMITED
      );
    }
  } catch (error) {
    if (error instanceof ApiError) {
      throw error;
    }
    logger.error('Error validating recovery attempt:', error);
    // Don't block on validation errors, but log them
  }
}

/**
 * Validate authentication attempt against rate limits
 * @param accessKey Access key attempting authentication
 * @param req Next.js request
 * @throws ApiError if rate limited
 */
export async function validateAuthAttempt(accessKey: string, req: NextRequest): Promise<void> {
  // Check account-level lock
  const accountLock = await checkAccountLock(accessKey);
  if (accountLock.isLocked) {
    const remainingMinutes = accountLock.remainingTime ? Math.ceil(accountLock.remainingTime / (60 * 1000)) : 0;
    throw ApiError.tooManyRequests(
      `Account temporarily locked. Try again in ${remainingMinutes} minutes.`,
      ErrorCode.ACCOUNT_LOCKED
    );
  }

  // Check IP-level rate limit
  const ipLimit = await checkIPRateLimit(req);
  if (ipLimit.isLimited) {
    const remainingMinutes = ipLimit.remainingTime ? Math.ceil(ipLimit.remainingTime / (60 * 1000)) : 0;
    throw ApiError.tooManyRequests(
      `Too many failed attempts from this IP. Try again in ${remainingMinutes} minutes.`,
      ErrorCode.IP_RATE_LIMITED
    );
  }
}
